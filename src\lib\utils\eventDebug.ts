/* eslint-disable @typescript-eslint/no-explicit-any */
export const eventDebugUtils = {
  /**
   * Check what's currently stored in localStorage for events
   */
  checkLocalStorage: () => {
    const eventStore = localStorage.getItem('event-store');
    console.log('📦 Event Store in localStorage:', eventStore ? JSON.parse(eventStore) : 'Not found');
    return eventStore ? JSON.parse(eventStore) : null;
  },

  /**
   * Clear all event-related data from localStorage
   */
  clearLocalStorage: () => {
    localStorage.removeItem('event-store');
    console.log('🧹 Cleared event-store from localStorage');
  },

  /**
   * Compare API response with localStorage data
   */
  compareApiWithStorage: (apiData: any) => {
    const storageData = eventDebugUtils.checkLocalStorage();
    console.log('🔍 Data Comparison:', {
      api: {
        events: apiData?.data?.events?.length || 0,
        meta: apiData?.data?.meta
      },
      storage: {
        events: storageData?.state?.userEvents?.length || 0,
        selectedEvent: storageData?.state?.selectedEvent?.title || 'None',
        meta: storageData?.state?.eventsMeta
      }
    });
  },

  /**
   * Force refresh by clearing cache and localStorage
   */
  forceRefresh: () => {
    eventDebugUtils.clearLocalStorage();
    // Clear React Query cache
    if (window.location) {
      window.location.reload();
    }
  }
};

// Make it available globally for debugging in browser console
if (typeof window !== 'undefined') {
  (window as any).eventDebug = eventDebugUtils;
}
