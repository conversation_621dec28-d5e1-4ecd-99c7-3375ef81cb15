/* eslint-disable @typescript-eslint/no-explicit-any */
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { useEventStore, CreatedEventData } from '../store/event';
import { events } from '../services/events';
import { useEffect, useCallback } from 'react';
import { toast } from 'react-toastify';
import { eventDebugUtils } from '../utils/eventDebug';

export const useEventManagement = () => {
  const {
    userEvents,
    selectedEvent,
    setSelectedEvent,
    setUserEvents,
    clearAllEventData: clearStoreData,
  } = useEventStore();
  const queryClient = useQueryClient();
  const { data, isLoading, isError, error, refetch, isFetching } = useQuery({
    queryKey: ['userEvents'],
    queryFn: () => events.getEventForAuthUsers(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    refetchOnMount: 'always',
    refetchOnWindowFocus: true,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  useEffect(() => {
    if (data?.data) {
      const events = data.data.events || [];
      const meta = data.data.meta || null;
      console.log('🔄 Syncing events with API response:', {
        apiEvents: events.length,
        storeEvents: userEvents.length,
        apiMeta: meta,
        timestamp: new Date().toISOString(),
      });
      eventDebugUtils.compareApiWithStorage(data);

      setUserEvents(events, meta);
      if (events.length === 0 && selectedEvent) {
        console.log('🧹 Clearing selected event as no events available');
        setSelectedEvent(null);
      }
    }
  }, [data, setUserEvents, selectedEvent, setSelectedEvent, userEvents.length]);

  useEffect(() => {
    if (isError) {
      console.error('Failed to fetch user events:', error);
      toast.error('Failed to load events. Please try again.');
    }
  }, [isError, error]);
  const refreshEvents = useCallback(async () => {
    await queryClient.invalidateQueries({ queryKey: ['userEvents'] });
    return refetch();
  }, [queryClient, refetch]);

  const clearAllEventData = useCallback(() => {
    queryClient.removeQueries({ queryKey: ['userEvents'] });
    clearStoreData();
  }, [queryClient, clearStoreData]);
  const addEventOptimistically = useCallback(
    (newEvent: CreatedEventData) => {
      queryClient.setQueryData(['userEvents'], (oldData: any) => {
        if (!oldData?.data?.events) return oldData;

        return {
          ...oldData,
          data: {
            ...oldData.data,
            events: [newEvent, ...oldData.data.events],
            meta: {
              ...oldData.data.meta,
              total: (oldData.data.meta?.total || 0) + 1,
            },
          },
        };
      });
      const updatedEvents = [newEvent, ...userEvents];
      setUserEvents(updatedEvents, data?.data?.meta);

      if (!selectedEvent) {
        setSelectedEvent(newEvent);
      }
    },
    [
      queryClient,
      userEvents,
      data?.data?.meta,
      setUserEvents,
      selectedEvent,
      setSelectedEvent,
    ]
  );
  const removeEventOptimistically = useCallback(
    (eventId: string) => {
      queryClient.setQueryData(['userEvents'], (oldData: any) => {
        if (!oldData?.data?.events) return oldData;

        const filteredEvents = oldData.data.events.filter(
          (event: CreatedEventData) => event.id !== eventId
        );

        return {
          ...oldData,
          data: {
            ...oldData.data,
            events: filteredEvents,
            meta: {
              ...oldData.data.meta,
              total: Math.max((oldData.data.meta?.total || 0) - 1, 0),
            },
          },
        };
      });

      const filteredEvents = userEvents.filter((event) => event.id !== eventId);
      setUserEvents(filteredEvents, data?.data?.meta);
      if (selectedEvent?.id === eventId && filteredEvents.length > 0) {
        setSelectedEvent(filteredEvents[0]);
      } else if (selectedEvent?.id === eventId) {
        setSelectedEvent(null);
      }
    },
    [
      queryClient,
      userEvents,
      data?.data?.meta,
      setUserEvents,
      selectedEvent,
      setSelectedEvent,
    ]
  );
  const updateEventOptimistically = useCallback(
    (updatedEvent: CreatedEventData) => {
      queryClient.setQueryData(['userEvents'], (oldData: any) => {
        if (!oldData?.data?.events) return oldData;

        const updatedEvents = oldData.data.events.map(
          (event: CreatedEventData) =>
            event.id === updatedEvent.id ? updatedEvent : event
        );

        return {
          ...oldData,
          data: {
            ...oldData.data,
            events: updatedEvents,
          },
        };
      });
      const updatedEvents = userEvents.map((event) =>
        event.id === updatedEvent.id ? updatedEvent : event
      );
      setUserEvents(updatedEvents, data?.data?.meta);
      if (selectedEvent?.id === updatedEvent.id) {
        setSelectedEvent(updatedEvent);
      }
    },
    [
      queryClient,
      userEvents,
      data?.data?.meta,
      setUserEvents,
      selectedEvent,
      setSelectedEvent,
    ]
  );

  const createEventMutation = useMutation({
    mutationFn: events.createEvent,
    onSuccess: (response) => {
      const newEvent = response.data;
      addEventOptimistically(newEvent);
      toast.success('Event created successfully!');
    },
    onError: (error: any) => {
      console.error('Error creating event:', error);
      toast.error(error?.response?.data?.message || 'Failed to create event');
      refreshEvents();
    },
  });

  return {
    // Data
    userEvents,
    selectedEvent,
    isLoading,
    isError,
    isFetching,
    error,

    // Actions
    setSelectedEvent,
    refreshEvents,
    clearAllEventData,
    addEventOptimistically,
    removeEventOptimistically,
    updateEventOptimistically,
    createEventMutation,

    // Raw query data for advanced use cases
    rawData: data,
    refetch,

    // Debug utilities
    debugUtils: eventDebugUtils,
  };
};
