/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useRef } from 'react';
import { TickCircle, Notepad2 } from 'iconsax-react';
import { Icon } from '../../../components/icons/icon';
import scan from '../../../assets/animations/scanniing.gif';
import { useEventStore } from '../../../lib/store/event';
import { AuthServices } from '../../../lib/services/auth';
import { toast } from 'react-toastify';
import { useMutation, useQueryClient } from '@tanstack/react-query';

interface UploadGuestListProps {
  onNextStep?: () => void;
  onFormActiveChange?: (isActive: boolean) => void;
}

export const UploadGuestList = ({ onNextStep }: UploadGuestListProps) => {
  const queryClient = useQueryClient();
  const { selectedEvent } = useEventStore();
  const [file, setFile] = useState<File | null>(null);
  const [fileName, setFileName] = useState<string>('');
  const [isScanning, setIsScanning] = useState<boolean>(false);
  const [isComplete, setIsComplete] = useState<boolean>(false);
  const [fileError, setFileError] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const uploadMutation = useMutation({
    mutationFn: async (fileToUpload: File) => {
      if (!selectedEvent?.id) {
        throw new Error('No event selected');
      }
      return AuthServices.uploadFiles(
        fileToUpload,
        'guestlist',
        selectedEvent.id
      );
    },
    onSuccess: () => {
      toast.success('Guest list uploaded successfully!');
      queryClient.invalidateQueries({ queryKey: ['userEvents'] });
      queryClient.invalidateQueries({
        queryKey: ['guestList', selectedEvent?.id],
      });
      if (onNextStep) onNextStep();
    },
    onError: (error: any) => {
      console.error('❌ Failed to upload guest list:', error);
      const errorMessage =
        error?.response?.data?.message || 'Failed to upload guest list file';
      toast.error(errorMessage);
    },
  });

  const isValidFileType = (file: File): boolean => {
    const validTypes = [
      '.csv',
      '.xlsx',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/csv',
    ];
    return validTypes.some(
      (type) => file.name.toLowerCase().endsWith(type) || file.type === type
    );
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      setFileError('');

      if (!isValidFileType(selectedFile)) {
        setFileError('Invalid file type. Please upload a CSV or XLSX file.');
        return;
      }

      setFile(selectedFile);
      setFileName(selectedFile.name);
      setIsScanning(true);

      setTimeout(() => {
        setIsScanning(false);
        setIsComplete(true);
      }, 1500);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const droppedFile = e.dataTransfer.files?.[0];
    if (droppedFile) {
      setFileError('');

      if (!isValidFileType(droppedFile)) {
        setFileError('Invalid file type. Please upload a CSV or XLSX file.');
        return;
      }

      setFile(droppedFile);
      setFileName(droppedFile.name);
      setIsScanning(true);

      setTimeout(() => {
        setIsScanning(false);
        setIsComplete(true);
      }, 2000);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleUploadRecord = () => {
    if (isComplete && file) {
      uploadMutation.mutate(file);
    }
  };

  const handleDownloadTemplate = () => {
    const templateUrl = '/template.xlsx';
    const link = document.createElement('a');
    link.href = templateUrl;
    link.download = 'template.xlsx';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="flex-1 pt-8 px-2 md:px-0">
      <h3 className="md:text-[28px] text-lg font-medium">Upload Guest list</h3>
      <p className="md:text-base text-sm text-grey-250 mb-5">
        Got a documented list? upload right away!
      </p>

      <div className="mb-4 text-xs relative">
        <div className="bg-primary-150  py-1.5 pl-3 pr-12 rounded-2xl w-fit">
          <span className="text-primary-500 font-medium">
            Hey!, we helped you with a template
          </span>
        </div>
        <button
          onClick={handleDownloadTemplate}
          className="md:absolute -right-1 top-[3px] text-primary flex items-center gap-1 rounded-full py-0.5 px-2 bg-white border border-primary-950 italic font-bold">
          Download Template
          <Icon name="helpCircle" />
        </button>
      </div>
      <div
        className={` ${
          isComplete ? 'bg-primary-150' : 'bg-grey-350'
        } flex flex-col md:flex-row rounded-2xl  mb-2 cursor-pointer`}
        onClick={handleUploadClick}
        onDrop={handleDrop}
        onDragOver={handleDragOver}>
        <div
          className={` ${
            isComplete ? 'md:bg-primary-250' : 'md:bg-grey-850'
          } px-2 h-full mx-auto py-4 rounded-l-2xl`}>
          <Notepad2
            size={90}
            color={isComplete ? '#B8BBFA' : '#B3B3B3'}
            variant="Bulk"
            className="opacity-70"
          />
        </div>
        <div className="flex flex-col justify-between  w-full text-center md:text-start">
          <div className="md:ml-4 my-6">
            <h3
              className={`italic text-base font-medium ${
                isComplete ? 'text-primary-750' : 'text-black'
              }`}>
              {isComplete ? <> {fileName}</> : ' No file Uploaded yet '}
            </h3>
            <p
              className={`text-xs ${
                isComplete ? 'text-primary-500' : 'text-grey-550 '
              }`}>
              {isComplete
                ? 'Click to upload or change document'
                : 'Click to upload document'}
            </p>
          </div>
          <p
            className={`text-[10px] italic  py-[7px] rounded-br-2xl  ${
              isComplete
                ? 'text-primary-500 bg-primary-250'
                : 'text-dark-200 md:bg-grey-850'
            }`}>
            <span className="font-extrabold">Note:</span> Acceptable docs
            include (.CSV, .XLSX)
          </p>
        </div>
      </div>
      {fileError && <p className="text-red-500 text-sm mb-4">{fileError}</p>}
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        onChange={handleFileChange}
      />

      {(isScanning || uploadMutation.isPending) && (
        <div className="flex items-center gap-2 border border-grey-150 w-fit py-1 px-3 rounded-full">
          <img src={scan} alt="scanning" className="h-4 w-4" />
          <p className="text-grey-500 text-sm font-medium">
            {uploadMutation.isPending ? 'Uploading...' : 'Scanning Document...'}
          </p>
        </div>
      )}

      {isComplete && !uploadMutation.isPending && (
        <div className="flex items-center gap-2 italic font-semibold text-sm">
          <p className="text-grey-550 py-1 px-3 rounded-full bg-grey-850">
            Record Found
          </p>
          <div className="flex items-center gap-2 text-green-600 bg-green-50 px-3 py-1 rounded-full">
            <TickCircle size={16} color="#22C55E" variant="Bold" />
            <span className="text-sm font-medium">No Errors found</span>
          </div>
        </div>
      )}

      <div className="mt-38 py-3.5 border-t border-grey-850 flex justify-end">
        <button
          onClick={handleUploadRecord}
          disabled={!isComplete || uploadMutation.isPending}
          className={`bg-primary text-white font-semibold py-3 px-6 rounded-full ${
            !isComplete || uploadMutation.isPending
              ? 'opacity-50 cursor-not-allowed'
              : 'cursor-pointer hover:bg-primary/90'
          }`}>
          {uploadMutation.isPending ? 'Uploading...' : 'Upload Guest Record'}
        </button>
      </div>
    </div>
  );
};
