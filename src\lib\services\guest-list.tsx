import { EventParkAPI } from '../event-park-api';

export interface TemplateCategory {
  id: string;
  name: string;
}

export interface GetTemplatesParams {
  category_id?: string;
  from?: string;
  to?: string;
  page?: number;
  per_page?: number;
  status?: 'active' | 'disabled';
}

export const GuestList = {
  getTemplates: async (params?: GetTemplatesParams) => {
    return await EventParkAPI().get('/v1/templates', { params });
  },
  getTemplatesCategories: async () => {
    return await EventParkAPI().get('/v1/templates/categories');
  },
};
