/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  events,
  GuestData,
  CreateGuestListPayload,
  CreateGuestFilePayload,
  GuestParams,
} from '../services/events';
import { toast } from 'react-toastify';
import { useCallback } from 'react';

export const useGuestListManagement = () => {
  const queryClient = useQueryClient();

  const createGuestListMutation = useMutation({
    mutationFn: (payload: CreateGuestListPayload) =>
      events.createGuestList(payload),
    onSuccess: (response, variables) => {
      console.log('✅ Guest list created successfully:', response);
      toast.success('Guest list created successfully!');

      queryClient.invalidateQueries({ queryKey: ['userEvents'] });
      queryClient.invalidateQueries({ queryKey: ['guestList', variables.id] });
    },
    onError: (error: any) => {
      const errorMessage =
        error?.response?.data?.message || 'Failed to create guest list';
      toast.error(errorMessage);
    },
  });

  // Mutation for creating guest list from file upload
  const createGuestListFromFileMutation = useMutation({
    mutationFn: (payload: CreateGuestFilePayload) =>
      events.createGuestListFromFile(payload),
    onSuccess: (response, variables) => {
      console.log('✅ Guest list created from file successfully:', response);
      toast.success('Guest list uploaded successfully!');

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['userEvents'] });
      queryClient.invalidateQueries({ queryKey: ['guestList', variables.id] });
    },
    onError: (error: any) => {
      console.error('❌ Error uploading guest list:', error);
      const errorMessage =
        error?.response?.data?.message || 'Failed to upload guest list';
      toast.error(errorMessage);
    },
  });

  // Helper function to create guest list with proper validation
  const createGuestList = useCallback(
    async (
      eventId: string,
      guests: GuestData[],
      inviteType: string = 'manual'
    ) => {
      if (!eventId) {
        throw new Error('Event ID is required');
      }

      if (!guests || guests.length === 0) {
        throw new Error('At least one guest is required');
      }
      const invalidGuests = guests.filter(
        (guest) =>
          !guest.email ||
          !guest.first_name ||
          !guest.last_name ||
          !guest.phone_number
      );

      if (invalidGuests.length > 0) {
        throw new Error(
          'All guests must have email, first name, last name, and phone number'
        );
      }

      const payload: CreateGuestListPayload = {
        id: eventId,
        guests,
        invite_type: inviteType,
      };

      console.log('🚀 Creating guest list with payload:', payload);
      return createGuestListMutation.mutateAsync(payload);
    },
    [createGuestListMutation]
  );

  // Helper function to upload guest list from file
  const uploadGuestListFile = useCallback(
    async (eventId: string, file: File, inviteType: string = 'file_upload') => {
      if (!eventId) {
        throw new Error('Event ID is required');
      }

      if (!file) {
        throw new Error('File is required');
      }

      // Validate file type (optional - you can add specific validations)
      const allowedTypes = ['.csv', '.xlsx', '.xls'];
      const fileExtension = file.name
        .toLowerCase()
        .substring(file.name.lastIndexOf('.'));

      if (!allowedTypes.includes(fileExtension)) {
        throw new Error('Please upload a CSV or Excel file');
      }

      const payload: CreateGuestFilePayload = {
        id: eventId,
        file,
        invite_type: inviteType,
      };

      console.log('📁 Uploading guest list file:', {
        eventId,
        fileName: file.name,
        fileSize: file.size,
      });
      return createGuestListFromFileMutation.mutateAsync(payload);
    },
    [createGuestListFromFileMutation]
  );

  const transformGuestData = useCallback(
    (
      uiGuests: Array<{
        firstName: string;
        lastName: string;
        email: string;
        phone: string;
      }>
    ): GuestData[] => {
      return uiGuests.map((guest) => ({
        email: guest.email.trim().toLowerCase(),
        first_name: guest.firstName.trim(),
        last_name: guest.lastName.trim(),
        phone_number: guest.phone.startsWith('+')
          ? guest.phone
          : `+234${guest.phone}`, // Add country code if missing
      }));
    },
    []
  );

  return {
    // Mutations
    createGuestListMutation,
    createGuestListFromFileMutation,

    // Helper functions
    createGuestList,
    uploadGuestListFile,
    transformGuestData,

    // Loading states
    isCreatingGuestList: createGuestListMutation.isPending,
    isUploadingFile: createGuestListFromFileMutation.isPending,
    isLoading:
      createGuestListMutation.isPending ||
      createGuestListFromFileMutation.isPending,

    // Error states
    createError: createGuestListMutation.error,
    uploadError: createGuestListFromFileMutation.error,
  };
};

export const useGuests = (id: string, params?: GuestParams) => {
  return useQuery({
    queryKey: ['guests', id, params],
    queryFn: () => events.getGuestsForAnAuthUser(id, params),
    enabled: !!id,
  });
};
